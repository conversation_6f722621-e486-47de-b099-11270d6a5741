<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word文档解析器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 头部 -->
        <header class="bg-white shadow-sm border-b rounded-lg mb-8">
            <div class="px-6 py-6">
                <div class="flex items-center">
                    <svg class="h-8 w-8 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h1 class="text-2xl font-bold text-gray-900">Word文档解析器</h1>
                </div>
                <p class="text-sm text-gray-500 mt-2">支持批量处理 • 自定义模板 • 标题提取</p>
            </div>
        </header>

        <!-- 主要内容 -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="mb-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-2">上传Word文档</h2>
                <p class="text-gray-600">
                    支持 .docx 和 .doc 格式，可以单个或批量上传文档。系统将自动提取文档中的标题内容。
                </p>
            </div>

            <!-- 文件上传区域 -->
            <div id="dropzone" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 transition-colors">
                <svg class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">拖拽文档到此处或点击选择</h3>
                <p class="text-gray-500">支持 .docx 和 .doc 格式，支持批量上传</p>
                <input type="file" id="fileInput" multiple accept=".docx,.doc" class="hidden">
            </div>

            <!-- 模板配置 -->
            <div class="mt-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4">提取模板</h3>
                <textarea id="template" rows="6" class="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 font-mono text-sm" placeholder="输入模板，例如：&#10;{测评项目概述}&#10;{测评项目概述-测评目的}&#10;{总体评价}">{测评项目概述}
{测评项目概述-测评目的}
{总体评价}</textarea>
            </div>

            <!-- 处理按钮 -->
            <div class="mt-6 flex justify-end">
                <button id="processBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    开始处理
                </button>
            </div>

            <!-- 结果显示 -->
            <div id="results" class="mt-8 hidden">
                <h3 class="text-lg font-medium text-gray-900 mb-4">处理结果</h3>
                <div id="resultContent" class="space-y-4"></div>
            </div>

            <!-- 支持的标题样式说明 -->
            <div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-md">
                <h4 class="text-sm font-medium text-blue-800 mb-2">支持的标题样式</h4>
                <div class="text-sm text-blue-700">
                    <p class="mb-1">• 标题、标题 1、标题 2、标题 3、标题 4</p>
                    <p>• 附录一级标题、附录二级标题、附录三级标题、附录四级标题</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const dropzone = document.getElementById('dropzone');
        const fileInput = document.getElementById('fileInput');
        const processBtn = document.getElementById('processBtn');
        const template = document.getElementById('template');
        const results = document.getElementById('results');
        const resultContent = document.getElementById('resultContent');

        let selectedFiles = [];

        // 拖拽功能
        dropzone.addEventListener('click', () => fileInput.click());
        dropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropzone.classList.add('border-blue-400', 'bg-blue-50');
        });
        dropzone.addEventListener('dragleave', () => {
            dropzone.classList.remove('border-blue-400', 'bg-blue-50');
        });
        dropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropzone.classList.remove('border-blue-400', 'bg-blue-50');
            handleFiles(e.dataTransfer.files);
        });

        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            selectedFiles = Array.from(files).filter(file => 
                file.name.endsWith('.docx') || file.name.endsWith('.doc')
            );
            
            if (selectedFiles.length > 0) {
                dropzone.innerHTML = `
                    <svg class="h-12 w-12 text-green-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">已选择 ${selectedFiles.length} 个文档</h3>
                    <p class="text-gray-500">${selectedFiles.map(f => f.name).join(', ')}</p>
                `;
                processBtn.disabled = false;
            }
        }

        // 处理文档
        processBtn.addEventListener('click', async () => {
            if (selectedFiles.length === 0) return;

            processBtn.disabled = true;
            processBtn.textContent = '处理中...';

            try {
                const formData = new FormData();
                if (selectedFiles.length === 1) {
                    formData.append('document', selectedFiles[0]);
                    const response = await axios.post('http://localhost:3001/api/upload', formData);
                    await applyTemplate([response.data]);
                } else {
                    selectedFiles.forEach(file => formData.append('documents', file));
                    const response = await axios.post('http://localhost:3001/api/upload-batch', formData);
                    await applyTemplate(response.data.results);
                }
            } catch (error) {
                console.error('处理失败:', error);
                alert('处理失败: ' + (error.response?.data?.error || error.message));
            } finally {
                processBtn.disabled = false;
                processBtn.textContent = '开始处理';
            }
        });

        async function applyTemplate(documents) {
            const templateText = template.value;
            const results = [];

            for (const doc of documents) {
                if (!doc.success) {
                    results.push({
                        filename: doc.filename,
                        success: false,
                        error: doc.error
                    });
                    continue;
                }

                try {
                    const response = await axios.post('http://localhost:3001/api/apply-template', {
                        headings: doc.headings,
                        template: templateText
                    });
                    results.push({
                        filename: doc.filename,
                        success: true,
                        result: response.data.result
                    });
                } catch (error) {
                    results.push({
                        filename: doc.filename,
                        success: false,
                        error: error.message
                    });
                }
            }

            displayResults(results);
        }

        function displayResults(results) {
            resultContent.innerHTML = '';
            
            results.forEach(item => {
                const div = document.createElement('div');
                div.className = `border rounded-lg ${item.success ? 'border-green-200' : 'border-red-200'}`;
                
                if (item.success) {
                    div.innerHTML = `
                        <div class="px-4 py-3 bg-green-50 border-b border-green-200">
                            <h4 class="font-medium text-gray-900">${item.filename}</h4>
                        </div>
                        <div class="p-4">
                            ${Object.entries(item.result).map(([key, value]) => `
                                <div class="border-l-4 border-blue-400 pl-4 mb-4">
                                    <h5 class="font-medium text-gray-900 mb-1">${key}</h5>
                                    <p class="text-gray-700 whitespace-pre-wrap">${value || '(未找到匹配内容)'}</p>
                                </div>
                            `).join('')}
                        </div>
                    `;
                } else {
                    div.innerHTML = `
                        <div class="px-4 py-3 bg-red-50 border-b border-red-200">
                            <h4 class="font-medium text-gray-900">${item.filename}</h4>
                            <p class="text-sm text-red-600 mt-1">${item.error}</p>
                        </div>
                    `;
                }
                
                resultContent.appendChild(div);
            });

            results.style.display = 'block';
        }

        // 测试后端连接
        async function testBackend() {
            try {
                const response = await axios.get('http://localhost:3001/api/health');
                console.log('后端连接正常:', response.data);
            } catch (error) {
                console.error('后端连接失败:', error);
                alert('无法连接到后端服务，请确保后端服务器正在运行在端口3001');
            }
        }

        // 页面加载时测试后端连接
        testBackend();
    </script>
</body>
</html>
